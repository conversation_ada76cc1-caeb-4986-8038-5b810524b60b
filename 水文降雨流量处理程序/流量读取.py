"""
流量数据读取管理器
负责流量数据的读取、处理和展示
"""
import sys
import os
import pyodbc
import pandas as pd
import numpy as np
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QDialog, QVBoxLayout, QScrollArea, QWidget, QPushButton,
    QProgressDialog, QMessageBox, QLabel, QDesktopWidget, QHBoxLayout, QFrame, QFileDialog,
    QTableWidget, QTableWidgetItem, QHeaderView
)
from PyQt5.QtGui import QIcon
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer

def create_access_connection(db_path):
    """创建Access数据库连接，尝试多种驱动"""
    connection_strings = [
        r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};" + fr"DBQ={db_path}",
        r"DRIVER={Microsoft Access Driver (*.mdb)};" + fr"DBQ={db_path}",
        r"DRIVER={Microsoft Office 16.0 Access Database Engine OLE DB Provider};" + fr"Data Source={db_path}",
    ]

    for conn_str in connection_strings:
        try:
            print(f"尝试连接字符串: {conn_str}")
            conn = pyodbc.connect(conn_str)
            print("连接成功!")
            return conn
        except Exception as e:
            print(f"连接失败: {e}")
            continue

    raise Exception("所有Access驱动连接方式都失败")

# -----------------------------------------------------------
# 颜色主题
# -----------------------------------------------------------
COLORS = {
    'primary': "#2563EB", 'secondary': "#3B82F6", 'accent': "#10B981",
    'background': "#0F172A", 'surface': "#1E293B", 'card': "#334155",
    'border': "#475569", 'text_primary': "#F8FAFC", 'text_muted': "#94A3B8"
}

# -----------------------------------------------------------
# RainDataReader —— 读取雨量信息
# -----------------------------------------------------------
class RainDataReader(QThread):
    progress_updated = pyqtSignal(str)
    data_loaded = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, db_path):
        super().__init__()
        self.db_path = db_path

    def run(self):
        try:
            self.progress_updated.emit("连接雨量数据库…")
            conn = pyodbc.connect(
                r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                fr"DBQ={self.db_path}"
            )
            self.progress_updated.emit("读取数据…")
            df_station = pd.read_sql("SELECT * FROM [P-Station]", conn)
            df_data    = pd.read_sql("SELECT * FROM [表]", conn)
            conn.close()

            key_col = 2
            keys = np.unique(df_data.values[:, key_col])
            station_arr = df_station.values

            info = {}
            for k in keys:
                recs = df_data[df_data.iloc[:, key_col] == k]
                base = next((row for row in station_arr if str(row[2]) == str(k)), None)
                info[k] = dict(name=str(k), record_count=len(recs),
                               has_station_info=base is not None)
            self.data_loaded.emit(info)
        except Exception as e:
            self.error_occurred.emit(f"读取雨量数据错误：{e}")

# -----------------------------------------------------------
# FlowDataReader —— 仅提取流量站名
# -----------------------------------------------------------
class FlowDataReader(QThread):
    progress_updated = pyqtSignal(str)
    data_loaded = pyqtSignal(list)
    error_occurred = pyqtSignal(str)

    def __init__(self, db_path):
        super().__init__()
        self.db_path = db_path

    def run(self):
        try:
            self.progress_updated.emit("连接流量数据库…")
            conn = create_access_connection(self.db_path)

            self.progress_updated.emit("读取 a01流量站表…")
            df = pd.read_sql("SELECT * FROM [a01流量站表]", conn)
            conn.close()
            stations = df.iloc[:, 1].dropna().astype(str).tolist()
            self.data_loaded.emit(stations)
        except Exception as e:
            self.error_occurred.emit(f"读取流量站错误：{e}")

# -----------------------------------------------------------
# FlowTableReader —— 流量表数据读取线程（若需异步读取，可使用）
# -----------------------------------------------------------
class FlowTableReader(QThread):
    """流量表数据读取线程"""
    progress_updated = pyqtSignal(str)
    data_loaded = pyqtSignal(pd.DataFrame, str)  # DataFrame和站点名称
    error_occurred = pyqtSignal(str)

    def __init__(self, db_path, station_name):
        super().__init__()
        self.db_path = db_path
        self.station_name = station_name

    def run(self):
        try:
            table_name = f"{self.station_name}流量"
            print(f"FlowTableReader开始读取表: {table_name}")

            self.progress_updated.emit(f"正在连接数据库...")
            conn_str = (
                r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                fr"DBQ={self.db_path}"
            )

            self.progress_updated.emit(f"正在读取 {table_name} 表...")
            with pyodbc.connect(conn_str) as conn:
                df = pd.read_sql(f"SELECT * FROM [{table_name}]", conn)

            self.progress_updated.emit(f"数据读取完成，共 {len(df)} 条记录")
            print(f"FlowTableReader读取完成: {len(df)} 条记录")
            self.data_loaded.emit(df, self.station_name)

        except Exception as e:
            error_msg = f"读取表 {self.station_name}流量 失败：{str(e)}"
            print(f"FlowTableReader错误: {error_msg}")
            self.error_occurred.emit(error_msg)

# -----------------------------------------------------------
# StationInfoDialog —— 雨量站统计表
# -----------------------------------------------------------
class StationInfoDialog(QDialog):
    def __init__(self, info_dict, parent=None):
        super().__init__(parent)
        self.setWindowTitle(f"雨量站信息（{len(info_dict)} 个）")
        scr = QDesktopWidget().screenGeometry()
        self.resize(int(scr.width()*0.6), int(scr.height()*0.7))

        layout = QVBoxLayout(self)
        table = QTableWidget()
        layout.addWidget(table)
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["站点", "记录数", "基础信息"])
        table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        table.setRowCount(len(info_dict))
        for r, (k, v) in enumerate(sorted(info_dict.items(),
                                        key=lambda x: x[1]['record_count'], reverse=True)):
            table.setItem(r, 0, QTableWidgetItem(str(k)))
            table.setItem(r, 1, QTableWidgetItem(f"{v['record_count']:,}"))
            table.setItem(r, 2, QTableWidgetItem("✓" if v['has_station_info'] else "✗"))

# -----------------------------------------------------------
# FloodTableDialog —— 展示洪水场次表
# -----------------------------------------------------------
class FloodTableDialog(QDialog):
    """展示洪水场次表：列 ID、开始时间、结束时间"""
    def __init__(self, df: pd.DataFrame, station_name: str, parent=None):
        super().__init__(parent)
        self.setWindowTitle(f"{station_name} 洪水场次表（共 {len(df)} 条记录）")

        # 调用MainWindow的handle_flood_data方法
        # 需要找到MainWindow实例
        main_win = parent
        while main_win and not hasattr(main_win, 'handle_flood_data'):
            main_win = main_win.parent() if hasattr(main_win, 'parent') else None
        if main_win and hasattr(main_win, 'handle_flood_data'):
            main_win.handle_flood_data(df, station_name)
        scr = QDesktopWidget().screenGeometry()
        self.resize(int(scr.width()*0.6), int(scr.height()*0.8))
        self.move((scr.width() - self.width()) // 2, (scr.height() - self.height()) // 2)

        layout = QVBoxLayout(self)
        info_label = QLabel(f"站点：{station_name}    洪水场次记录数：{len(df)} 条")
        info_label.setStyleSheet("""
            QLabel {
                font-size: 14px; font-weight: bold; color: #2563EB;
                padding: 8px; background: #f0f4f8; border-radius: 4px;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(info_label)

        table = QTableWidget()
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["ID", "开始时间", "结束时间"])
        table.setStyleSheet("""
            QTableWidget {
                background-color: white; border: 1px solid #d1d5db;
                border-radius: 6px; font-size: 12px;
                gridline-color: #e5e7eb; selection-background-color: #3b82f6;
            }
            QTableWidget::item {
                padding: 6px 8px; border-bottom: 1px solid #f3f4f6;
            }
            QHeaderView::section {
                background-color: #f8fafc; color: #374151;
                padding: 8px; border: none; font-weight: bold;
                border-bottom: 2px solid #d1d5db;
            }
        """)
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Stretch)

        table.setRowCount(len(df))
        for r in range(len(df)):
            # ID
            id_val = df.iloc[r, 0]
            table.setItem(r, 0, QTableWidgetItem(str(id_val)))
            # 开始时间 (第3列)
            start_val = df.iloc[r, 2] if df.shape[1] > 2 else None
            table.setItem(r, 1, QTableWidgetItem(str(start_val)))
            # 结束时间 (第4列)
            end_val = df.iloc[r, 3] if df.shape[1] > 3 else None
            table.setItem(r, 2, QTableWidgetItem(str(end_val)))

        layout.addWidget(table)

        close_btn = QPushButton("关闭")
        close_btn.setStyleSheet("""
            QPushButton {
                background: #3b82f6; color: white; border: none;
                border-radius: 6px; padding: 8px 16px; font-size: 12px;
                font-weight: bold; margin-top: 8px;
            }
            QPushButton:hover { background: #2563eb; }
            QPushButton:pressed { background: #1d4ed8; }
        """)
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)

# -----------------------------------------------------------
# FlowTableDialog —— 展示“站名流量”表，并增加查看洪水场次功能
# -----------------------------------------------------------
class FlowTableDialog(QDialog):
    def __init__(self, df: pd.DataFrame, station_name: str, db_path: str, parent=None):
        super().__init__(parent)
        self.station_name = station_name
        self.db_path = db_path  # 保存数据库路径
        self.setWindowTitle(f"{station_name} 流量表（共 {len(df)} 条记录）")

        # 调用MainWindow的handle_flow_data方法
        if hasattr(parent, 'handle_flow_data'):
            parent.handle_flow_data(df, station_name)

        scr = QDesktopWidget().screenGeometry()
        self.resize(int(scr.width()*0.6), int(scr.height()*0.8))
        self.move((scr.width() - self.width()) // 2, (scr.height() - self.height()) // 2)

        layout = QVBoxLayout(self)

        # 信息标签
        info_label = QLabel(f"站点：{station_name}    数据条数：{len(df)} 条")
        info_label.setStyleSheet("""
            QLabel {
                font-size: 14px; font-weight: bold; color: #2563EB;
                padding: 8px; background: #f0f4f8; border-radius: 4px;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(info_label)

        # 流量表格
        table = QTableWidget()
        table.setColumnCount(2)
        table.setHorizontalHeaderLabels(["时间", "流量 (m³/s)"])
        table.setStyleSheet("""
            QTableWidget {
                background-color: white; border: 1px solid #d1d5db;
                border-radius: 6px; font-size: 12px;
                gridline-color: #e5e7eb; selection-background-color: #3b82f6;
            }
            QTableWidget::item {
                padding: 6px 8px; border-bottom: 1px solid #f3f4f6;
            }
            QHeaderView::section {
                background-color: #f8fafc; color: #374151;
                padding: 8px; border: none; font-weight: bold;
                border-bottom: 2px solid #d1d5db;
            }
        """)
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # 时间列自适应
        table.setColumnWidth(1, 120)  # 流量列固定宽度

        # 填充数据
        table.setRowCount(len(df))
        for r, (time_val, flow_val) in enumerate(zip(df.iloc[:, 0], df.iloc[:, 1])):
            # 时间列
            time_item = QTableWidgetItem(str(time_val))
            table.setItem(r, 0, time_item)

            # 流量列 - 格式化为2位小数
            if pd.notna(flow_val):
                try:
                    flow_formatted = f"{float(flow_val):.2f}"
                except (ValueError, TypeError):
                    flow_formatted = str(flow_val)
            else:
                flow_formatted = "N/A"
            flow_item = QTableWidgetItem(flow_formatted)
            table.setItem(r, 1, flow_item)

        layout.addWidget(table)

        # ---- 新增：查看洪水场次按钮 ----
        flood_btn = QPushButton("查看洪水场次")
        flood_btn.setStyleSheet("""
            QPushButton {
                background: #10B981; color: white; border: none;
                border-radius: 6px; padding: 8px 16px; font-size: 12px;
                font-weight: bold; margin-top: 8px;
            }
            QPushButton:hover { background: #059669; }
            QPushButton:pressed { background: #047857; }
        """)
        flood_btn.clicked.connect(self._on_view_flood_events)
        layout.addWidget(flood_btn)

        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.setStyleSheet("""
            QPushButton {
                background: #3b82f6; color: white; border: none;
                border-radius: 6px; padding: 8px 16px; font-size: 12px;
                font-weight: bold; margin-top: 8px;
            }
            QPushButton:hover { background: #2563eb; }
            QPushButton:pressed { background: #1d4ed8; }
        """)
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)

    def _on_view_flood_events(self):
        """读取并展示 洪水场次 表"""
        table_name = f"{self.station_name}洪水场次"
        try:
            conn_str = (
                r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                fr"DBQ={self.db_path}"
            )
            with pyodbc.connect(conn_str) as conn:
                # 读取所有列，展示时只取所需列
                df = pd.read_sql(f"SELECT * FROM [{table_name}]", conn)
            if df.empty:
                QMessageBox.information(self, "提示", f"表 {table_name} 无数据或为空。")
                return
            # 展示洪水场次对话框
            dlg = FloodTableDialog(df, self.station_name, parent=self)
            dlg.setAttribute(Qt.WA_DeleteOnClose, False)
            dlg.show()
            dlg.raise_()
            dlg.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取表 {table_name} 失败：{e}")

# -----------------------------------------------------------
# FlowStationDialog —— 列出流量站按钮 & 查询对应表
# -----------------------------------------------------------
class FlowStationDialog(QDialog):
    def __init__(self, station_names, db_path, parent=None):
        super().__init__(parent)
        self.db_path = db_path  # 保存数据库路径
        self.setWindowTitle(f"流量站选择（{len(station_names)} 站）")
        scr = QDesktopWidget().screenGeometry()
        self.resize(int(scr.width()*0.4), int(scr.height()*0.6))

        layout = QVBoxLayout(self)
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        inner = QWidget()
        il = QVBoxLayout(inner)

        for name in station_names:
            btn = QPushButton(name)
            btn.setStyleSheet("""
                QPushButton{
                    background:#3b82f6;color:white;padding:10px;
                    border:none;border-radius:8px;margin-bottom:8px;
                }
                QPushButton:hover{background:#2563eb;}
            """)
            btn.clicked.connect(lambda _, n=name: self._load_flow_table(n))
            il.addWidget(btn)
        il.addStretch()
        scroll.setWidget(inner)
        layout.addWidget(scroll)

    def _load_flow_table(self, station_name):
        table_name = f"{station_name}流量"
        try:
            conn_str = (
                r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                fr"DBQ={self.db_path}"
            )
            with pyodbc.connect(conn_str) as conn:
                df = pd.read_sql(f"SELECT * FROM [{table_name}]", conn)
            if df.empty:
                QMessageBox.information(self, "提示", f"表 {table_name} 无数据。")
                return
            # 传递MainWindow作为parent，以便FlowTableDialog能调用handle_flow_data
            main_win = self.parent()
            dlg = FlowTableDialog(df, station_name, self.db_path, parent=main_win)
            dlg.setAttribute(Qt.WA_DeleteOnClose, False)
            dlg.show()
            dlg.raise_()
            dlg.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取表 {table_name} 失败：{e}")

# -----------------------------------------------------------
# MainWindow
# -----------------------------------------------------------
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("水文降雨流量插值处理程序")

        self.default_db_paths = {
            'rain':  r"C:\Users\<USER>\Desktop\雨量数据.accdb",
            'flow':  r"C:\Users\<USER>\Desktop\流量数据.accdb",
            'output':r"C:\Users\<USER>\Desktop\输出数据库.accdb"
        }

        scr = QDesktopWidget().screenGeometry()
        self.resize(int(scr.width()*0.8), int(scr.height()*0.8))
        self.move((scr.width()-self.width())//2, (scr.height()-self.height())//2)

        self._init_ui()
        self._check_default_databases()

    # -------------------- UI --------------------
    def _init_ui(self):
        central = QWidget()
        self.setCentralWidget(central)
        main = QHBoxLayout(central)
        self.flow_btn, self.flow_status = self._add_db_control(main, "流量", "flow")
        self.rain_btn, self.rain_status = self._add_db_control(main, "降雨", "rain")
        self.output_btn,  self.output_status  = self._add_db_control(main, "输出", "output")

    def _add_db_control(self, layout, label, key):
        panel = QFrame()
        v = QVBoxLayout(panel)
        btn = QPushButton(f"连接{label}数据库")
        btn.setCheckable(True)
        status = QLabel(f"未连接{label}数据库")
        status.setProperty("connected","false")
        btn.clicked.connect(lambda _, b=btn, s=status: self._select_db(b,s))
        v.addWidget(btn)
        v.addWidget(status)
        layout.addWidget(panel,1)
        return btn, status

    # -------------------- 选择 / 自动连接 --------------------
    def _select_db(self, btn, status):
        path, _ = QFileDialog.getOpenFileName(
            self, "选择数据库", os.path.join(os.path.expanduser("~"),"Desktop"),
            "Database Files (*.accdb *.mdb *.db *.sqlite);;All Files(*)"
        )
        if path:
            self._update_status(btn, status, path)

    def _update_status(self, btn, status, path=""):
        ok = bool(path)
        status.setText((f"已连接: {os.path.basename(path)}") if ok else "未连接")
        status.setProperty("connected","true" if ok else "false")
        btn.setChecked(ok)
        btn.setToolTip(path)

        if ok and btn is self.flow_btn:
            self._auto_load_flow(path)
        if ok and btn is self.rain_btn:
            self._auto_load_rain(path)

    def _check_default_databases(self):
        for key, path in self.default_db_paths.items():
            if os.path.exists(path):
                btn = getattr(self, f"{key}_btn")
                st = getattr(self, f"{key}_status")
                self._update_status(btn, st, path)

    # -------------------- 雨量流程 --------------------
    def _auto_load_rain(self, db_path):
        self.rain_progress = QProgressDialog("读取雨量数据…","取消",0,0,self)
        self.rain_progress.show()

        self.rain_reader = RainDataReader(db_path)
        self.rain_reader.progress_updated.connect(self.rain_progress.setLabelText, Qt.QueuedConnection)
        self.rain_reader.data_loaded.connect(lambda d: self._show_rain(d, self.rain_progress), Qt.QueuedConnection)
        self.rain_reader.error_occurred.connect(lambda e: QMessageBox.critical(self,"错误",e), Qt.QueuedConnection)
        self.rain_reader.finished.connect(self._cleanup_rain_reader, Qt.QueuedConnection)
        self.rain_reader.start()
        self.rain_progress.canceled.connect(self._cancel_rain_reader)

    def _cleanup_rain_reader(self):
        """清理雨量数据读取线程"""
        if hasattr(self, 'rain_reader'):
            self.rain_reader.deleteLater()
            delattr(self, 'rain_reader')

    def _cancel_rain_reader(self):
        """取消雨量数据读取"""
        if hasattr(self, 'rain_reader') and self.rain_reader.isRunning():
            self.rain_reader.terminate()
            self.rain_reader.wait(3000)

    def _show_rain(self, info_dict, pdialog):
        try:
            pdialog.close()
            # 使用QTimer延迟创建对话框，确保在主线程中执行
            QTimer.singleShot(100, lambda: self._create_rain_dialog(info_dict))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示雨量数据时发生错误: {str(e)}")

    def _create_rain_dialog(self, info_dict):
        """在主线程中安全创建雨量站对话框"""
        try:
            self.rain_dialog = StationInfoDialog(info_dict, self)
            self.rain_dialog.setAttribute(Qt.WA_DeleteOnClose, False)
            self.rain_dialog.show()
            self.rain_dialog.raise_()
            self.rain_dialog.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建雨量站对话框时发生错误: {str(e)}")

    # -------------------- 流量流程 --------------------
    def _auto_load_flow(self, db_path):
        self.flow_progress = QProgressDialog("读取流量站列表…","取消",0,0,self)
        self.flow_progress.show()

        self.flow_reader = FlowDataReader(db_path)
        self.flow_reader.progress_updated.connect(self.flow_progress.setLabelText, Qt.QueuedConnection)
        self.flow_reader.data_loaded.connect(lambda lst: self._show_flow(lst, db_path, self.flow_progress), Qt.QueuedConnection)
        self.flow_reader.error_occurred.connect(lambda e: QMessageBox.critical(self,"错误",e), Qt.QueuedConnection)
        self.flow_reader.finished.connect(self._cleanup_flow_reader, Qt.QueuedConnection)
        self.flow_reader.start()
        self.flow_progress.canceled.connect(self._cancel_flow_reader)

    def _cleanup_flow_reader(self):
        """清理流量数据读取线程"""
        if hasattr(self, 'flow_reader'):
            self.flow_reader.deleteLater()
            delattr(self, 'flow_reader')

    def _cancel_flow_reader(self):
        """取消流量数据读取"""
        if hasattr(self, 'flow_reader') and self.flow_reader.isRunning():
            self.flow_reader.terminate()
            self.flow_reader.wait(3000)

    def _show_flow(self, stations, db_path, pdialog):
        try:
            pdialog.close()
            # 使用QTimer延迟创建对话框，确保在主线程中执行
            QTimer.singleShot(100, lambda: self._create_flow_dialog(stations, db_path))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示流量数据时发生错误: {str(e)}")

    def _create_flow_dialog(self, stations, db_path):
        """在主线程中安全创建流量站对话框"""
        try:
            self.flow_dialog = FlowStationDialog(stations, db_path, self)
            self.flow_dialog.setAttribute(Qt.WA_DeleteOnClose, False)
            self.flow_dialog.show()
            self.flow_dialog.raise_()
            self.flow_dialog.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建流量站对话框时发生错误: {str(e)}")

    def closeEvent(self, event):
        """窗口关闭时清理所有线程"""
        try:
            # 清理雨量数据读取线程
            if hasattr(self, 'rain_reader') and self.rain_reader.isRunning():
                self.rain_reader.terminate()
                self.rain_reader.wait(3000)

            # 清理流量数据读取线程
            if hasattr(self, 'flow_reader') and self.flow_reader.isRunning():
                self.flow_reader.terminate()
                self.flow_reader.wait(3000)

            event.accept()
        except Exception as e:
            print(f"关闭窗口时发生错误: {e}")
            event.accept()

# -----------------------------------------------------------
# FlowDataManager —— 如需集中管理，可使用此类（可选）
# -----------------------------------------------------------
class FlowDataManager:
    """流量数据管理器"""

    def __init__(self, parent_window):
        self.parent = parent_window
        self.db_path = None  # 保存数据库路径

    def load_data(self, db_path):
        """加载流量数据"""
        try:
            print(f"开始自动加载流量数据: {db_path}")
            self.db_path = db_path  # 保存数据库路径供后续使用

            # 创建进度对话框
            self.progress_dialog = QProgressDialog("正在读取流量站信息...", "取消", 0, 0, self.parent)
            self.progress_dialog.setWindowTitle("流量数据读取")
            self.progress_dialog.setModal(True)
            self.progress_dialog.show()
            print("流量进度对话框已显示")

            # 创建并启动数据读取线程
            self.flow_reader = FlowDataReader(db_path)
            self.flow_reader.progress_updated.connect(self._update_progress, Qt.QueuedConnection)
            self.flow_reader.data_loaded.connect(self._on_data_loaded, Qt.QueuedConnection)
            self.flow_reader.error_occurred.connect(self._on_error, Qt.QueuedConnection)
            self.flow_reader.finished.connect(self._on_reading_finished, Qt.QueuedConnection)
            print("流量信号连接完成（使用QueuedConnection）")
            self.flow_reader.start()
            print("流量数据读取线程已启动")

            # 连接取消按钮
            self.progress_dialog.canceled.connect(self._cancel_reading)

        except Exception as e:
            print(f"启动流量数据读取时发生错误: {e}")
            QMessageBox.critical(self.parent, "错误", f"启动流量数据读取时发生错误: {str(e)}")

    def _update_progress(self, message):
        """更新进度信息"""
        print(f"流量进度更新: {message}")
        if hasattr(self, 'progress_dialog') and self.progress_dialog:
            self.progress_dialog.setLabelText(message)

    def _on_data_loaded(self, stations):
        """数据加载完成"""
        try:
            print(f"流量数据加载完成，收到{len(stations)}个站点")
            # 关闭进度对话框
            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                self.progress_dialog.close()
                self.progress_dialog = None
            # 使用QTimer延迟创建对话框，避免线程冲突
            QTimer.singleShot(100, lambda: self._create_station_dialog(stations))
        except Exception as e:
            print(f"显示流量数据时发生错误: {e}")
            QMessageBox.critical(self.parent, "错误", f"显示流量数据时发生错误: {str(e)}")

    def _create_station_dialog(self, stations):
        """在主线程中创建流量站对话框"""
        try:
            self.station_dialog = FlowStationDialog(stations, self.db_path, self.parent)
            self.station_dialog.setAttribute(Qt.WA_DeleteOnClose, False)
            self.station_dialog.show()
            self.station_dialog.raise_()
            self.station_dialog.activateWindow()
            print(f"成功显示流量站信息窗口，包含 {len(stations)} 个站点")
        except Exception as e:
            print(f"创建流量站对话框时发生错误: {e}")
            QMessageBox.critical(self.parent, "错误", f"创建流量站对话框时发生错误: {str(e)}")

    def _on_error(self, error_message):
        """处理错误"""
        print(f"流量数据读取错误: {error_message}")
        QMessageBox.critical(self.parent, "错误", error_message)

    def _on_reading_finished(self):
        """读取完成"""
        if hasattr(self, 'flow_reader'):
            self.flow_reader.deleteLater()
            delattr(self, 'flow_reader')

    def _cancel_reading(self):
        """取消读取"""
        if hasattr(self, 'flow_reader') and self.flow_reader.isRunning():
            self.flow_reader.terminate()
            self.flow_reader.wait()

# -----------------------------------------------------------
# 入口
# -----------------------------------------------------------
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setWindowIcon(QIcon())   # 可加载自定义图标
    MainWindow().show()
    sys.exit(app.exec_())
