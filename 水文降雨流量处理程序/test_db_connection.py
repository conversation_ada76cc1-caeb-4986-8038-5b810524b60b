#!/usr/bin/env python3
"""
数据库连接测试脚本
用于诊断Access数据库连接问题
"""
import pyodbc
import pandas as pd
import os

def test_connection(db_path):
    """测试数据库连接"""
    print(f"测试数据库: {db_path}")
    print(f"文件是否存在: {os.path.exists(db_path)}")
    
    if not os.path.exists(db_path):
        print("错误: 数据库文件不存在")
        return False
    
    # 测试不同的连接字符串
    connection_strings = [
        r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};" + fr"DBQ={db_path}",
        r"DRIVER={Microsoft Access Driver (*.mdb)};" + fr"DBQ={db_path}",
    ]
    
    for i, conn_str in enumerate(connection_strings, 1):
        print(f"\n尝试连接方式 {i}: {conn_str}")
        try:
            conn = pyodbc.connect(conn_str)
            print("✓ 连接成功!")
            
            # 测试查询表列表
            cursor = conn.cursor()
            tables = []
            for table_info in cursor.tables(tableType='TABLE'):
                tables.append(table_info.table_name)
            
            print(f"数据库中的表: {tables}")
            
            # 测试查询特定表
            if 'a01流量站表' in tables:
                print("测试查询 a01流量站表...")
                df = pd.read_sql("SELECT * FROM [a01流量站表]", conn)
                print(f"a01流量站表 记录数: {len(df)}")
                if len(df) > 0:
                    print(f"列名: {list(df.columns)}")
                    print(f"前5行数据:\n{df.head()}")
            else:
                print("未找到 a01流量站表")
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            continue
    
    print("所有连接方式都失败")
    return False

def main():
    """主函数"""
    print("=== Access数据库连接测试 ===\n")
    
    # 测试流量数据库
    flow_db = r"C:\Users\<USER>\Desktop\流量数据.accdb"
    print("1. 测试流量数据库:")
    test_connection(flow_db)
    
    print("\n" + "="*50 + "\n")
    
    # 测试雨量数据库
    rain_db = r"C:\Users\<USER>\Desktop\雨量数据.accdb"
    print("2. 测试雨量数据库:")
    test_connection(rain_db)

if __name__ == "__main__":
    main()
