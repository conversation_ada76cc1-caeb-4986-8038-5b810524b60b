import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates


def plot_flow_rain(time_series, flow_series, rain_series):
    """
    绘制双坐标流量-降雨图：
      - 左轴：流量折线；上限 = 1.25 × max(flow)
      - 右轴：倒挂降雨柱状；下限 = 4 × max(rain)
      - 柱宽自动：根据时间序列相邻时间差计算，使柱状图紧挨
      - x 轴日期刻度竖直显示，自动格式化

    参数:
        time_series: 可被 pd.to_datetime 解析的一维序列（list/array/Series），代表时间点
        flow_series: 可转为 float 的一维序列，与 time_series 等长，代表流量值
        rain_series: 可转为 float 的一维序列，与 time_series 等长，代表降雨值

    返回:
        fig: matplotlib.figure.Figure 对象，调用者可以进一步保存或调整
    """
    # ——准备数据——
    # 将时间转换为 pandas 的 DatetimeIndex，以便后续使用 AutoDateLocator/Formatter
    t = pd.to_datetime(time_series)
    # 将流量和降雨转换为 numpy 数组（float）
    y_flow = np.asarray(flow_series, dtype=float)
    y_rain = np.asarray(rain_series, dtype=float)

    if len(t) == 0:
        raise ValueError("时间序列长度为 0，无法绘图。")

    # 自动计算柱宽（单位：天），取相邻时间差的中位数
    if len(t) > 1:
        # 计算相邻时间差，以天为单位
        # np.diff(t) 是 timedelta64[ns] 类型，除以 np.timedelta64(1,'D') 得到浮点天数
        diffs_in_days = np.diff(t.values.astype('datetime64[ns]')) / np.timedelta64(1, 'D')
        # 取中位数作为柱宽参考
        width_days = float(np.median(diffs_in_days))
        # 为防止柱重叠过度，可略微减小宽度，比如 90% 或 95%
        width = width_days * 0.9
        # 如果 width 非常小或为 0，可给一个最小宽度
        if width <= 0:
            width = 0.02
    else:
        # 仅一个点时，给个默认宽度（以天为单位）
        width = 0.02

    max_flow = np.nanmax(y_flow) if y_flow.size > 0 else 0.0
    max_rain = np.nanmax(y_rain) if y_rain.size > 0 else 0.0

    # ——绘图——
    fig, ax_flow = plt.subplots(figsize=(10, 5))

    # 左轴：流量
    line_flow, = ax_flow.plot(t, y_flow, color='black', linewidth=1.5, label='Flow')
    ax_flow.set_xlabel("Time")
    ax_flow.set_ylabel("Flow", color='black')
    ax_flow.tick_params(axis='y', labelcolor='black')
    if max_flow > 0:
        ax_flow.set_ylim(0, max_flow * 1.25)
    else:
        ax_flow.set_ylim(auto=True)

    # 右轴：降雨
    ax_rain = ax_flow.twinx()
    # bar 需要传入 width，x 轴为 datetime，会自动转换；align='center' 使柱心在时间点上
    bars = ax_rain.bar(t, y_rain, width=width, align='center', color='blue', alpha=0.6, label='Rainfall')
    ax_rain.set_ylabel("Rainfall", color='blue')
    ax_rain.tick_params(axis='y', labelcolor='blue')
    if max_rain > 0:
        ax_rain.set_ylim(0, max_rain * 4)  # 先正向设置
        ax_rain.invert_yaxis()  # 再倒挂
    else:
        # 若没有有效降雨值，则保持默认，不做 invert
        pass

    # ——x 轴日期格式化——
    # 自动定位：根据数据密集度选择合适的刻度
    ax_flow.xaxis.set_major_locator(mdates.AutoDateLocator())
    # 格式化显示：年月日，可根据需要调整格式字符串
    ax_flow.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    # 旋转 x 轴刻度标签，竖直显示或接近竖直：
    plt.setp(ax_flow.get_xticklabels(), rotation=90, ha='center')

    # ——合并图例——
    # 从左右两轴分别获取 legend handles/labels，合并后放在适当位置
    handles_flow, labels_flow = ax_flow.get_legend_handles_labels()
    handles_rain, labels_rain = ax_rain.get_legend_handles_labels()
    handles = handles_flow + handles_rain
    labels = labels_flow + labels_rain
    if handles:
        # 放在图上方或右上角等，根据实际需要调整 loc
        ax_flow.legend(handles, labels, loc='upper left', bbox_to_anchor=(0, 1.0), fontsize='small')

    fig.tight_layout()
    # 若希望在函数内直接显示，可取消注释下一行
    # plt.show()

    return fig
