import sys
import os
import pandas as pd
import pyodbc
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, QPushButton,
    QFileDialog, QMessageBox, QFrame, QDesktopWidget, QLabel, QComboBox, QDialog
)
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtCore import Qt

# 导入数据读取模块
from 雨量读取 import RainDataManager
from 流量读取 import FlowDataManager, create_access_connection
from 绘图 import plot_flow_rain

# 现代深色主题配色方案
COLORS = {
    'primary': "#2563EB", 'secondary': "#3B82F6", 'accent': "#10B981", 'success': "#059669",
    'warning': "#F59E0B", 'danger': "#EF4444", 'background': "#0F172A", 'surface': "#1E293B",
    'card': "#334155", 'border': "#475569", 'text_primary': "#F8FAFC",
    'text_secondary': "#CBD5E1", 'text_muted': "#94A3B8"
}

# -----------------------------------------------------------
# FloodEventPlotDialog - 洪水场次绘图对话框
# -----------------------------------------------------------
class FloodEventPlotDialog(QDialog):
    """洪水场次选择和绘图对话框"""

    def __init__(self, df_flow, df_rain, df_flood, station_name, parent=None):
        super().__init__(parent)
        self.df_flow = df_flow
        self.df_rain = df_rain
        self.df_flood = df_flood
        self.station_name = station_name

        self.setWindowTitle(f"{station_name} - 洪水场次绘图")

        # 设置窗口大小
        screen = QDesktopWidget().screenGeometry()
        self.resize(int(screen.width() * 0.9), int(screen.height() * 0.8))
        self.move((screen.width() - self.width()) // 2, (screen.height() - self.height()) // 2)

        self._init_ui()

    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 顶部控制面板
        control_panel = QFrame()
        control_layout = QHBoxLayout(control_panel)

        # 场次选择标签
        label = QLabel("选择洪水场次:")
        label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2563EB;")
        control_layout.addWidget(label)

        # 场次选择下拉框
        self.event_combo = QComboBox()
        self.event_combo.setStyleSheet("""
            QComboBox {
                font-size: 12px; padding: 5px; border: 1px solid #d1d5db;
                border-radius: 4px; background: white; min-width: 200px;
            }
            QComboBox::drop-down {
                border: none; width: 20px;
            }
            QComboBox::down-arrow {
                image: none; border: 2px solid #6b7280;
                width: 6px; height: 6px;
            }
        """)

        # 填充场次选项
        for index, row in self.df_flood.iterrows():
            event_id = row.iloc[0]
            start_time = row.iloc[1] if len(row) > 1 else "未知"
            end_time = row.iloc[2] if len(row) > 2 else "未知"
            self.event_combo.addItem(f"场次 {event_id} ({start_time} ~ {end_time})", index)

        self.event_combo.currentIndexChanged.connect(self._on_event_changed)
        control_layout.addWidget(self.event_combo)

        control_layout.addStretch()

        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.setStyleSheet("""
            QPushButton {
                background: #ef4444; color: white; border: none;
                border-radius: 6px; padding: 8px 16px; font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover { background: #dc2626; }
        """)
        close_btn.clicked.connect(self.close)
        control_layout.addWidget(close_btn)

        layout.addWidget(control_panel)

        # 绘图区域
        self.figure = Figure(figsize=(12, 6))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)

        # 绘制第一个场次
        if len(self.df_flood) > 0:
            self._plot_event(0)


# -----------------------------------------------------------
# Main Window
# -----------------------------------------------------------
class MainWindow(QMainWindow):
    """水文降雨流量插值处理软件（GUI框架）。"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("水文降雨流量插值处理程序")

        # 默认数据库路径配置
        self.default_db_paths = {
            'rain': r"C:\Users\<USER>\Desktop\雨量数据.accdb",
            'flow': r"C:\Users\<USER>\Desktop\流量数据.accdb",
            'output': r"C:\Users\<USER>\Desktop\输出数据库.accdb"
        }

        # 获取屏幕尺寸并设置响应式窗口
        screen = QDesktopWidget().screenGeometry()
        self.screen_width, self.screen_height = screen.width(), screen.height()

        # 设置窗口尺寸（80%）和最小尺寸（50%）
        w, h = int(self.screen_width * 0.8), int(self.screen_height * 0.8)
        self.resize(w, h)
        self.setMinimumSize(int(self.screen_width * 0.5), int(self.screen_height * 0.5))
        self.move((self.screen_width - w) // 2, (self.screen_height - h) // 2)
        self._init_ui()

        #存储数据
        self.latest_flow_data = {}  # 存储 {station_name: DataFrame}
        self.latest_flood_data = {}  # 存储 {station_name: DataFrame}
        self.current_station = None
        self.flow_db_path = None
        self.rain_db_path = None
    # ------------------------------------------------------------------
    # 用户界面构建
    # ------------------------------------------------------------------
    def _init_ui(self):
        # 计算响应式尺寸
        sizes = {
            'base_font': max(12, int(self.screen_width * 0.012)),
            'border_radius': max(8, int(self.screen_width * 0.008)),
            'padding': max(12, int(self.screen_width * 0.012)),
            'margin': max(8, int(self.screen_width * 0.008)),
            'button_height': max(40, int(self.screen_height * 0.06))
        }
        sizes.update({
            'large_font': int(sizes['base_font'] * 1.4),
            'button_font': int(sizes['base_font'] * 1.6)
        })

        # 设置样式表
        self.setStyleSheet(self._create_stylesheet(sizes))

        # 创建主布局
        central = QWidget()
        self.setCentralWidget(central)
        main_layout = QHBoxLayout(central)
        main_layout.setContentsMargins(sizes['margin'], sizes['margin'], sizes['margin'], sizes['margin'])
        main_layout.setSpacing(sizes['padding'])

        # 创建左右面板
        self.file_panel = self._create_file_panel(sizes)
        self.data_panel = self._create_data_panel(sizes)

        main_layout.addWidget(self.file_panel, 1)
        main_layout.addWidget(self.data_panel, 2)

        # 检查并自动连接默认数据库
        self._check_default_databases()

    # ------------------------------------------------------------------
    # 辅助方法
    # ------------------------------------------------------------------
    def _create_stylesheet(self, sizes: dict) -> str:
        """创建响应式样式表"""
        c = COLORS  # 简化颜色引用
        return f"""
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 {c['background']}, stop:1 #1a202c);
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif; font-size: {sizes['base_font']}px;
            }}
            QLabel {{ color: {c['text_primary']}; font-size: {sizes['large_font']}px; font-weight: 600; }}
            QLabel[objectName="status_label"] {{
                color: {c['text_secondary']}; font-size: {sizes['base_font']}px; font-weight: 400;
                background: {c['card']}; border: 1px solid {c['border']}; border-radius: {sizes['border_radius']//2}px;
                padding: {sizes['padding']//2}px {sizes['padding']}px; margin: 2px 0px;
            }}
            QLabel[objectName="status_label"][connected="true"] {{
                color: {c['accent']}; border: 1px solid {c['accent']};
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(16,185,129,0.1), stop:1 rgba(5,150,105,0.1));
            }}
            QLabel[objectName="status_label"][connected="false"] {{ color: {c['text_muted']}; border: 1px solid {c['border']}; }}
            QFrame#panel {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {c['surface']}, stop:1 {c['card']});
                border: 1px solid {c['border']}; border-radius: {sizes['border_radius']}px;
                padding: {sizes['padding']}px; margin: {sizes['margin']}px;
            }}
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {c['surface']}, stop:1 {c['card']});
                border: 1px solid {c['border']}; border-radius: {sizes['border_radius']}px;
                padding: {sizes['padding']}px {int(sizes['padding']*1.2)}px; color: {c['text_primary']};
                font-size: {sizes['button_font']}px; font-weight: 600; text-align: center; min-height: {sizes['button_height']}px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {c['primary']}, stop:1 {c['secondary']});
                border: 1px solid {c['primary']}; color: {c['text_primary']};
            }}
            QPushButton:checked {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {c['accent']}, stop:1 {c['success']});
                border: 1px solid {c['accent']}; color: {c['text_primary']}; font-weight: 600;
            }}
            QPushButton:pressed {{ background: {c['card']}; border: 1px solid {c['border']}; }}
            QPushButton[objectName="data_btn"] {{
                border-radius: {sizes['border_radius']+2}px; padding: {int(sizes['padding']*1.5)}px {int(sizes['padding']*1.8)}px;
                margin: {sizes['margin']}px; font-size: {int(sizes['button_font']*1.2)}px; min-height: {int(sizes['button_height']*1.2)}px;
            }}
            QPushButton[objectName="data_btn"]:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {c['secondary']}, stop:1 {c['primary']});
                border: 1px solid {c['secondary']}; font-size: {int(sizes['button_font']*1.2)}px;
            }}
        """

    def _create_file_panel(self, sizes: dict) -> QFrame:
        """创建文件连接面板"""
        panel = QFrame(objectName="panel")
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(*(int(sizes['padding'] * 1.5),) * 4)
        layout.setSpacing(sizes['padding'] // 2)

        # 创建数据库连接组件
        db_types = [("流量", "flow"), ("降雨", "rain"), ("输出", "output")]
        for name, attr in db_types:
            btn = self._make_toggle_button(f"{name}数据库连接", sizes['button_height'])
            status = self._make_status_label(f"未连接{name}数据库")
            setattr(self, f"{attr}_btn", btn)
            setattr(self, f"{attr}_status", status)
            btn.clicked.connect(lambda checked, b=btn, s=status: self._select_db(b, s))
            layout.addWidget(btn)
            layout.addWidget(status)
            layout.addSpacing(sizes['padding'] // 2)

        layout.addStretch(1)
        return panel

    def _create_data_panel(self, sizes: dict) -> QFrame:
        """创建数据处理面板"""
        panel = QFrame(objectName="panel")
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(*(int(sizes['padding'] * 1.5),) * 4)
        layout.setSpacing(sizes['padding'])

        # 创建按钮并绑定不同的功能
        button_configs = [
            ("初始数据展示", self._on_initial_display),
            ("洪水场次确定", self._placeholder),
            ("降雨数据插值处理", self._placeholder),
            ("流量数据插值处理", self._placeholder),
            ("插值后数据展示", self._placeholder)
        ]

        for name, handler in button_configs:
            btn = QPushButton(name)
            btn.setObjectName("data_btn")
            btn.setMinimumHeight(int(sizes['button_height'] * 1.2))
            btn.clicked.connect(handler)
            layout.addWidget(btn)

        layout.addStretch(1)
        return panel

    def _make_toggle_button(self, text: str, height: int) -> QPushButton:
        btn = QPushButton(text)
        btn.setMinimumHeight(height)
        btn.setCheckable(True)
        return btn

    def _make_status_label(self, text: str) -> QLabel:
        """创建状态标签"""
        label = QLabel(text)
        label.setObjectName("status_label")
        label.setProperty("connected", "false")
        label.setWordWrap(True)
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        return label

    def _select_db(self, btn: QPushButton, status: QLabel):
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        path, _ = QFileDialog.getOpenFileName(
            self, "选择数据库文件", desktop,
            "Database Files (*.accdb *.mdb *.db *.sqlite);;All Files(*)")
        if path: self._update_status(btn, status, path)

    def _update_status(self, btn: QPushButton, status_label: QLabel, path: str = ""):
        """更新按钮和状态标签"""
        if path:
            btn.setChecked(True)
            btn.setToolTip(path)
            status_label.setText(f"已连接: {os.path.basename(path)}")
            status_label.setProperty("connected", "true")
            status_label.setToolTip(path)

            # 如果是雨量数据库连接成功，自动读取并展示数据
            if btn == getattr(self, 'rain_btn', None):
                self.rain_db_path = path
                self.rain_manager = RainDataManager(self)
                self.rain_manager.load_data(path)
            elif btn == getattr(self, 'flow_btn', None):
                self.flow_db_path = path
                self.flow_manager = FlowDataManager(self)
                self.flow_manager.load_data(path)

        else:
            btn.setChecked(False)
            btn.setToolTip("")
            db_type = {"flow": "流量", "rain": "降雨", "output": "输出"}
            for attr, name in db_type.items():
                if btn == getattr(self, f"{attr}_btn"):
                    status_label.setText(f"未连接{name}数据库")
                    break
            status_label.setProperty("connected", "false")
            status_label.setToolTip("")

        # 刷新样式
        status_label.style().unpolish(status_label)
        status_label.style().polish(status_label)

    def _check_default_databases(self):
        for key, path in self.default_db_paths.items():
            if os.path.exists(path):
                btn = getattr(self, f"{key}_btn"); status = getattr(self, f"{key}_status")
                self._update_status(btn, status, path)

    # ------------------------------------------------------------------
    # 数据处理函数
    # ------------------------------------------------------------------
    def handle_flow_data(self, df: pd.DataFrame, station_name: str):
        """处理流量数据：保存并更新current_station"""
        self.latest_flow_data[station_name] = df
        self.current_station = station_name
        QMessageBox.information(self, "收到流量数据", f"已加载站点【{station_name}】流量，共 {len(df)} 条。")

    def handle_flood_data(self, df: pd.DataFrame, station_name: str):
        """处理洪水数据：保存洪水数据"""
        self.latest_flood_data[station_name] = df
        QMessageBox.information(self, "收到洪水场次数据", f"已加载站点【{station_name}】洪水，共 {len(df)} 条。")

    def _on_initial_display(self):
        """初始数据展示功能"""
        if not self.current_station:
            QMessageBox.warning(self, "提示", "请先加载流量数据后再点击'初始数据展示'。")
            return

        station = self.current_station
        df_flow = self.latest_flow_data.get(station)
        if df_flow is None:
            QMessageBox.warning(self, "提示", f"未找到站点【{station}】流量，请先加载。")
            return

        df_flood = self.latest_flood_data.get(station)
        # 若未加载洪水，提示是否立即读取
        if df_flood is None:
            reply = QMessageBox.question(
                self, "提示",
                f"尚未加载【{station}】洪水场次，是否立即读取？",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                if not self.flow_db_path:
                    QMessageBox.warning(self, "错误", "无法获取流量数据库路径。")
                    return
                try:
                    table_name = f"{station}洪水场次"
                    with create_access_connection(self.flow_db_path) as conn:
                        df_tmp = pd.read_sql(f"SELECT * FROM [{table_name}]", conn)
                    if df_tmp.empty:
                        QMessageBox.information(self, "提示", f"表 {table_name} 无数据或为空。")
                    else:
                        self.handle_flood_data(df_tmp, station)
                        df_flood = df_tmp
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"读取洪水失败：{e}")
                    df_flood = None
            else:
                df_flood = None

        # 调用展示或处理函数
        self.process_initial_data(df_flow, df_flood, station)

    def process_initial_data(self, df_flow: pd.DataFrame, df_flood: pd.DataFrame, station_name: str):
        """处理和展示初始数据"""
        info = f"站点：{station_name}\n流量记录数：{len(df_flow)}"
        info += f"\n洪水记录数：{len(df_flood)}" if df_flood is not None else "\n洪水数据：未加载"
        QMessageBox.information(self, "初始数据展示", info)

        # 调用绘图处理函数
        self.process_and_plot_data(df_flow, df_flood, station_name)

    def process_and_plot_data(self, df_flow: pd.DataFrame, df_flood: pd.DataFrame, station_name: str):
        """处理数据并按场次绘图"""
        try:
            # 获取降雨数据
            df_rain = self.get_rain_data(station_name)
            if df_rain is None:
                QMessageBox.warning(self, "警告", f"无法获取站点【{station_name}】的降雨数据，将仅绘制流量数据。")
                # 仅绘制流量数据
                self.plot_flow_only(df_flow, station_name)
                return

            # 如果有洪水场次数据，按场次绘图
            if df_flood is not None and not df_flood.empty:
                self.plot_by_flood_events(df_flow, df_rain, df_flood, station_name)
            else:
                # 没有洪水场次数据，绘制全部数据
                self.plot_all_data(df_flow, df_rain, station_name)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理和绘图时发生错误：{str(e)}")

    def get_rain_data(self, station_name: str) -> pd.DataFrame:
        """获取指定站点的降雨数据"""
        if not self.rain_db_path:
            return None

        try:
            with create_access_connection(self.rain_db_path) as conn:
                # 从雨量数据表中查询指定站点的数据
                query = f"SELECT * FROM [表] WHERE [站名] = '{station_name}'"
                df_rain = pd.read_sql(query, conn)

            if df_rain.empty:
                return None

            # 假设雨量数据表的结构：第一列是时间，第二列是降雨量，第三列是站名
            # 根据实际数据库结构调整列索引
            df_rain = df_rain.iloc[:, [0, 1]]  # 取时间和降雨量列
            df_rain.columns = ['时间', '降雨量']

            return df_rain

        except Exception as e:
            print(f"获取降雨数据失败：{e}")
            return None

    def plot_by_flood_events(self, df_flow: pd.DataFrame, df_rain: pd.DataFrame, df_flood: pd.DataFrame, station_name: str):
        """按洪水场次绘图"""
        try:
            # 假设洪水场次表的结构：ID、开始时间、结束时间
            for index, flood_event in df_flood.iterrows():
                start_time = pd.to_datetime(flood_event.iloc[1])  # 开始时间
                end_time = pd.to_datetime(flood_event.iloc[2])    # 结束时间
                event_id = flood_event.iloc[0]  # 场次ID

                # 筛选该场次的流量数据
                flow_mask = (pd.to_datetime(df_flow.iloc[:, 0]) >= start_time) & \
                           (pd.to_datetime(df_flow.iloc[:, 0]) <= end_time)
                event_flow = df_flow[flow_mask]

                # 筛选该场次的降雨数据
                rain_mask = (pd.to_datetime(df_rain.iloc[:, 0]) >= start_time) & \
                           (pd.to_datetime(df_rain.iloc[:, 0]) <= end_time)
                event_rain = df_rain[rain_mask]

                if not event_flow.empty and not event_rain.empty:
                    # 调用绘图函数
                    fig = plot_flow_rain(
                        event_flow.iloc[:, 0],  # 时间
                        event_flow.iloc[:, 1],  # 流量
                        event_rain.iloc[:, 1]   # 降雨
                    )
                    fig.suptitle(f"{station_name} - 洪水场次 {event_id}")
                    plt.show()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"按场次绘图失败：{str(e)}")

    def plot_all_data(self, df_flow: pd.DataFrame, df_rain: pd.DataFrame, station_name: str):
        """绘制全部数据"""
        try:
            # 合并时间序列，找到共同的时间范围
            flow_times = pd.to_datetime(df_flow.iloc[:, 0])
            rain_times = pd.to_datetime(df_rain.iloc[:, 0])

            # 找到时间交集
            min_time = max(flow_times.min(), rain_times.min())
            max_time = min(flow_times.max(), rain_times.max())

            # 筛选数据
            flow_mask = (flow_times >= min_time) & (flow_times <= max_time)
            rain_mask = (rain_times >= min_time) & (rain_times <= max_time)

            filtered_flow = df_flow[flow_mask]
            filtered_rain = df_rain[rain_mask]

            if not filtered_flow.empty and not filtered_rain.empty:
                fig = plot_flow_rain(
                    filtered_flow.iloc[:, 0],  # 时间
                    filtered_flow.iloc[:, 1],  # 流量
                    filtered_rain.iloc[:, 1]   # 降雨
                )
                fig.suptitle(f"{station_name} - 全部数据")
                plt.show()
            else:
                QMessageBox.warning(self, "警告", "没有找到时间重叠的流量和降雨数据。")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"绘制全部数据失败：{str(e)}")

    def plot_flow_only(self, df_flow: pd.DataFrame, station_name: str):
        """仅绘制流量数据"""
        try:
            # 创建空的降雨数据
            zero_rain = [0] * len(df_flow)

            fig = plot_flow_rain(
                df_flow.iloc[:, 0],  # 时间
                df_flow.iloc[:, 1],  # 流量
                zero_rain            # 零降雨
            )
            fig.suptitle(f"{station_name} - 流量数据（无降雨数据）")
            plt.show()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"绘制流量数据失败：{str(e)}")

    def _placeholder(self):
        """占位符对话框"""
        QMessageBox.information(self, "功能暂未实现", "此功能正在开发中，敬请期待！")

# -----------------------------------------------------------
# 程序入口
# -----------------------------------------------------------
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setWindowIcon(QIcon())   # 如有自定义图标可在此加载
    win = MainWindow(); win.show()
    sys.exit(app.exec_())
